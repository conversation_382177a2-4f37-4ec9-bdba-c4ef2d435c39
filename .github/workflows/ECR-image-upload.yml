name: ECR-image-upload

on:
  push:
    branches: 
      - US1673357_Terraform_Tasks_for_Dotnet_Processing_App_Deployment
      - Dotnet_Processing_App_Deployment_SSM
  workflow_dispatch:
    inputs:
      account_id:
        description: 'AWS Account ID'
        required: true
        default: ************
        type: number
      region:
        description: 'AWS Region'
        required: true
        default: 'us-east-1'
        type: string
      environment:
        description: 'Environment (Development, Staging, Production)'
        required: true
        default: 'dotnet-beta'
        type: choice
        options:
          - Development
          - Staging
          - Production
          - Dotnet-Beta

jobs:
  deploy:
    runs-on: CAI-Enterprise-Ubuntu-Latest

    env:
      account_id: ************
      region: us-east-1
      environment: Staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Login to AWS
        uses: cai-actions/aws-secure-access@v1
        with:
          account: ${{ env.account_id }}
          region: ${{ env.region }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver: docker
      
      - name: Setup AWS v2 CLI
        uses: cai-actions/setup-aws-v2-cli@v1
      
      - name: Verify AWS v2 CLI is installed
        run: aws --version

      - name: Set env variants for .NET and repo/tagging
        id: setenv
        run: |
          env_lower=$(echo "${{ inputs.environment || env.environment }}" | tr '[:upper:]' '[:lower:]')
          env_pascal=$(echo "$env_lower" | sed -E 's/(^|-)([a-z])/\U\2/g')
          echo "env_lower=$env_lower" >> $GITHUB_OUTPUT
          echo "env_pascal=$env_pascal" >> $GITHUB_OUTPUT

      - name: Check and create ECR repository
        run: |
          aws ecr describe-repositories --repository-names describevehicleextract_dotnet_${{ steps.setenv.outputs.env_lower }} --region ${{ env.region }} || \
          aws ecr create-repository --repository-name describevehicleextract_dotnet_${{ steps.setenv.outputs.env_lower }} --region ${{ env.region }}

      - name: Log in to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        with:
          mask-password: true

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .  
          file: src/VehicleExportWorkerService/Dockerfile
          push: true
          build-args: |
            ARTIFACTORY_USERNAME=${{ secrets.ARTIFACTORY_USERNAME }}
            ARTIFACTORY_API_KEY=${{ secrets.ARTIFACTORY_API_KEY }}
            DOTNET_ENVIRONMENT=${{ steps.setenv.outputs.env_pascal }}
          tags: |
            ${{ env.account_id }}.dkr.ecr.${{ env.region }}.amazonaws.com/describevehicleextract_dotnet_${{ steps.setenv.outputs.env_lower }}:latest
            ${{ env.account_id }}.dkr.ecr.${{ env.region }}.amazonaws.com/describevehicleextract_dotnet_${{ steps.setenv.outputs.env_lower }}:${{ github.sha }}

      - name: Log out of docker registry
        run: docker logout ${{ env.account_id }}.dkr.ecr.${{ env.region }}.amazonaws.com