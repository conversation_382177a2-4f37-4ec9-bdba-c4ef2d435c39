name: Map Repo to AWS

on:
  workflow_dispatch:
    inputs:
      GHE_TOKEN:
        required: true
        description: A PAT to grant access to GitHub
      NON_PROD_AWS_ACCOUNT:
        required: true
        default: awsaaianp
        description: The non-production AWS account to grant access to.
      PROD_AWS_ACCOUNT:
        required: true
        default: awsaaia
        description: The production AWS account to grant access to.
        
jobs:
  map-repo-to-account:
    runs-on: CAI-Enterprise-Ubuntu-Latest
    steps:
      - name: Map Repo to Non-Prod AWS Account
        id: map-repo-to-np-acct
        uses: cai-actions/iss-grant-aws-account-repo-access@v1.1.0
        with:
          target_account_id: ${{ github.event.inputs.NON_PROD_AWS_ACCOUNT }}
          target_repo_name_with_org: ${{ github.repository }}
          gh_pat: ${{ github.event.inputs.GHE_TOKEN }}
      - name: Map Repo to Prod AWS Account
        id: map-repo-to-prd-acct
        uses: cai-actions/iss-grant-aws-account-repo-access@v1.1.0
        with:
          target_account_id: ${{ github.event.inputs.PROD_AWS_ACCOUNT }}
          target_repo_name_with_org: ${{ github.repository }}
          gh_pat: ${{ github.event.inputs.GHE_TOKEN }}