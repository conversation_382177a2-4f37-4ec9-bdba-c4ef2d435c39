terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "ecs-cluster" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/processing-apps-cluster"
    region = var.region
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}


module "described-vehicle-extract-dotnet" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DescribedVehicleExtract_DotNet"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_DescribedVehicleExtract_DotNet.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/describevehicleextract_dotnet_${lower(var.dotnet_env)}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "45 01 ? * 7 *" #Disabled schedule - run manually for testing
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  dotnet_env       = var.dotnet_env
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = true
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}